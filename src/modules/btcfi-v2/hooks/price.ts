import { BtcfiV2CustodyContractType } from '@/modules/btcfi/types';
import { Token } from '@/wallets/config/type';
import { useMemo } from 'react';
import { Address, parseUnits } from 'viem';
import { useConfig } from 'wagmi';
import { abi as ybtcStakingAbi } from '../abi/ybtc-staking-vault';
import { useQuery } from '@tanstack/react-query';
import { Config, readContract } from '@wagmi/core';

const PRICE_DECIMALS = 18n;

// Calculate LP amount from YBTC.B amount
export function lpFrom(
  token: Token,
  amount: bigint, // YBTC.B amount in 18 decimals
  lp: Token,
  price: bigint, // LP/YBTC.B price in 18 decimals
): bigint {
  // Formula: LP = (YBTC.B * price) / 10^(PRICE_DECIMALS + (YBTC_DECIMALS - LP_DECIMALS))
  return (
    (amount * 10n ** (PRICE_DECIMALS + (BigInt(token.decimals) - BigInt(lp.decimals)))) / price
  );
}

// Calculate YBTC.B amount from LP amount
export function fromLp(
  lp: Token,
  amount: bigint, // LP amount in 8 decimals
  token: Token,
  price: bigint, // LP/YBTC.B price in 18 decimals
): bigint {
  // Formula: YBTC.B = (LP * 10^(PRICE_DECIMALS + (YBTC_DECIMALS - LP_DECIMALS))) / price
  return (
    (amount * price) / 10n ** (PRICE_DECIMALS + (BigInt(token.decimals) - BigInt(lp.decimals)))
  );
}

type UseLpPriceParams = {
  token: Token;
  lp: Token;
  amount: string;
  contract: Address;
  contractType: BtcfiV2CustodyContractType;
};

export function useLpFrom({ token, lp, amount, contract, contractType }: UseLpPriceParams) {
  const { data: price } = usePrice(contract, contractType);
  return useMemo(() => {
    if (!price) {
      return undefined;
    }

    let amountBigInt: bigint | undefined = undefined;
    try {
      amountBigInt = parseUnits(amount, token.decimals);
    } catch (error) {
      return undefined;
    }
    return lpFrom(token, amountBigInt, lp, price);
  }, [token, lp, amount, price]);
}

export function useFromLp({ lp, token, amount, contract, contractType }: UseLpPriceParams) {
  const { data: price } = usePrice(contract, contractType);
  return useMemo(() => {
    if (!price) {
      return undefined;
    }

    let amountBigInt: bigint | undefined = undefined;
    try {
      amountBigInt = parseUnits(amount, lp.decimals);
    } catch (error) {
      return undefined;
    }
    return fromLp(lp, amountBigInt, token, price);
  }, [lp, token, amount, price]);
}

function usePrice(contract: Address, contractType: BtcfiV2CustodyContractType) {
  const config = useConfig() as Config;
  return useQuery({
    queryKey: ['btcfi-v2/price', contract, contractType] as const,
    queryFn: async () => {
      console.log('contractType', contractType);
      if (contractType !== 'btcfi-v2') {
        return 1_000_000_000_000_000_000n;
      }
      const data = await readContract(config, {
        abi: ybtcStakingAbi,
        address: contract,
        functionName: 'getCurrentPrice',
        args: [],
      });
      return data;
    },
  });
}
